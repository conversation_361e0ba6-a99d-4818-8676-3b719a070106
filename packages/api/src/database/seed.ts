/**
 * Database seeding script for development and testing.
 * This script populates the database with sample data including demo clients,
 * scraped pages, suggestions, and analytics for testing the AI Answer Bot system.
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import db from './connection';

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Test database connection
    const isConnected = await db.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    // Create demo clients
    console.log('👥 Creating demo clients...');
    
    const demoClients = [
      {
        id: uuidv4(),
        name: 'Acme Corporation',
        domain: 'acme.com',
        apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
        config: {
          primaryColor: '#667eea',
          askAnythingText: 'How can we help you?',
          logo: 'https://via.placeholder.com/40x40/667eea/ffffff?text=A',
          placeholder: 'Ask us anything about our products...',
          siteName: 'Acme Corporation'
        }
      },
      {
        id: uuidv4(),
        name: 'TechStart Inc',
        domain: 'techstart.io',
        apiKey: `ak_${crypto.randomBytes(32).toString('hex')}`,
        config: {
          primaryColor: '#10b981',
          askAnythingText: 'Need help?',
          logo: 'https://via.placeholder.com/40x40/10b981/ffffff?text=T',
          placeholder: 'What would you like to know?',
          siteName: 'TechStart'
        }
      }
    ];

    for (const client of demoClients) {
      await db.query(
        'INSERT INTO clients (id, name, domain, api_key, config) VALUES ($1, $2, $3, $4, $5)',
        [client.id, client.name, client.domain, client.apiKey, JSON.stringify(client.config)]
      );

      // Add sample scraped pages
      const samplePages = [
        {
          url: `https://${client.domain}/about`,
          title: 'About Us',
          content: 'Learn more about our company, mission, and values.',
          description: 'Company information and background'
        },
        {
          url: `https://${client.domain}/products`,
          title: 'Our Products',
          content: 'Explore our range of innovative products and services.',
          description: 'Product catalog and features'
        },
        {
          url: `https://${client.domain}/contact`,
          title: 'Contact Us',
          content: 'Get in touch with our team for support and inquiries.',
          description: 'Contact information and support'
        }
      ];

      for (const page of samplePages) {
        await db.query(
          'INSERT INTO scraped_pages (id, client_id, url, title, content, description) VALUES ($1, $2, $3, $4, $5, $6)',
          [uuidv4(), client.id, page.url, page.title, page.content, page.description]
        );
      }

      // Add sample suggestions
      const sampleSuggestions = [
        { question: 'What services do you offer?', category: 'general', priority: 10 },
        { question: 'How can I contact support?', category: 'support', priority: 9 },
        { question: 'What are your pricing plans?', category: 'pricing', priority: 8 },
        { question: 'How do I get started?', category: 'onboarding', priority: 7 },
        { question: 'Do you offer free trials?', category: 'pricing', priority: 6 }
      ];

      for (const suggestion of sampleSuggestions) {
        await db.query(
          'INSERT INTO bot_suggestions (id, client_id, question, category, priority) VALUES ($1, $2, $3, $4, $5)',
          [uuidv4(), client.id, suggestion.question, suggestion.category, suggestion.priority]
        );
      }

      console.log(`✅ Created demo client: ${client.name}`);
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Demo client details:');
    
    for (const client of demoClients) {
      console.log(`\n${client.name}:`);
      console.log(`  Domain: ${client.domain}`);
      console.log(`  API Key: ${client.apiKey}`);
      console.log(`  Client ID: ${client.id}`);
    }

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

export { seedDatabase };
