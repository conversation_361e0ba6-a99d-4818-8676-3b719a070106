/**
 * Demo application for the AI Answer Bot embed widget.
 * This component showcases the widget functionality with a mock website interface
 * and demonstrates responsive behavior across different screen sizes.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import Widget from './components/Widget';
import { BotConfig } from './types';

const Demo: React.FC = () => {
  const demoConfig: BotConfig = {
    clientId: 'b974bfed-a282-40c6-a091-0367a97feb77', // Acme Corporation from seed data
    apiUrl: 'http://localhost:3004/api',
    primaryColor: '#667eea',
    askAnythingText: 'How can we help?',
    logo: 'https://via.placeholder.com/40x40/667eea/ffffff?text=D',
    placeholder: 'Ask us anything about our demo...',
    siteName: 'Demo Website'
  };

  return (
    <div style={{
      margin: 0,
      padding: 0,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{
        maxWidth: '1200px',
        width: '100%',
        padding: '20px',
        position: 'relative'
      }}>
        <div style={{
          textAlign: 'center',
          color: 'white',
          marginBottom: '30px'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 700,
            margin: '0 0 10px 0'
          }}>
            AI Answer Bot Demo
          </h1>
          <p style={{
            fontSize: '1.2rem',
            opacity: 0.9,
            margin: 0
          }}>
            Experience intelligent customer support with customizable branding
          </p>
        </div>
        
        <div style={{
          background: 'white',
          borderRadius: '12px',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)',
          overflow: 'hidden',
          position: 'relative'
        }}>
          <div style={{
            background: '#f1f3f4',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            padding: '0 16px',
            borderBottom: '1px solid #e8eaed'
          }}>
            <div style={{ display: 'flex', gap: '8px' }}>
              <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#ff5f57' }}></div>
              <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#ffbd2e' }}></div>
              <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#28ca42' }}></div>
            </div>
            <div style={{
              background: 'white',
              border: '1px solid #e8eaed',
              borderRadius: '20px',
              padding: '8px 16px',
              marginLeft: '20px',
              flex: 1,
              maxWidth: '300px',
              fontSize: '14px',
              color: '#5f6368'
            }}>
              https://demo-website.com
            </div>
          </div>
          
          <div style={{
            padding: '40px',
            minHeight: '500px',
            background: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect width="1200" height="600" fill="%23f8f9fa"/><rect x="50" y="50" width="300" height="40" fill="%23e9ecef" rx="4"/><rect x="50" y="120" width="500" height="20" fill="%23dee2e6" rx="2"/><rect x="50" y="160" width="400" height="20" fill="%23dee2e6" rx="2"/><rect x="50" y="200" width="450" height="20" fill="%23dee2e6" rx="2"/><rect x="50" y="280" width="200" height="30" fill="%23667eea" rx="4"/><rect x="700" y="50" width="450" height="300" fill="%23e9ecef" rx="8"/></svg>') no-repeat center`,
            backgroundSize: 'cover'
          }}>
            {/* Website mockup content */}
          </div>
        </div>
      </div>

      {/* AI Answer Bot Widget */}
      <Widget 
        config={demoConfig}
        onMessage={(message) => console.log('Demo: User asked:', message)}
        onSuggestionClick={(suggestion) => console.log('Demo: Suggestion clicked:', suggestion)}
        onLinkClick={(link) => console.log('Demo: Link clicked:', link)}
      />
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(<Demo />);
