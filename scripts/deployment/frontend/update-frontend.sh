#!/bin/bash

# AWS Frontend Update Script
# This script builds and updates an existing deployment without recreating infrastructure

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
BUCKET_NAME="${BUCKET_NAME:-answer-bot.bloodandtreasure.com}"
ADMIN_BUILD_DIR="${ADMIN_BUILD_DIR:-packages/admin/dist}"
EMBED_BUILD_DIR="${EMBED_BUILD_DIR:-packages/embed/dist}"
CLOUDFRONT_COMMENT="${CLOUDFRONT_COMMENT:-AI Answer Bot Demo Frontend Distribution}"

# Note: Colors, logging functions, and check_aws_cli are now provided by global.sh

# Check if S3 bucket exists
check_s3_bucket() {
    log_info "Checking if S3 bucket '$BUCKET_NAME' exists..."
    
    if ! aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
        log_error "S3 bucket '$BUCKET_NAME' does not exist. Please run the full deployment script first."
        exit 1
    fi
    
    log_success "S3 bucket '$BUCKET_NAME' found"
}

# Get CloudFront distribution ID
get_distribution_id() {
    log_info "Finding CloudFront distribution..."
    
    local distribution_id=$(aws cloudfront list-distributions \
        --query "DistributionList.Items[?Comment=='$CLOUDFRONT_COMMENT'].Id" \
        --output text)
    
    if [ -z "$distribution_id" ] || [ "$distribution_id" = "None" ]; then
        log_error "CloudFront distribution not found. Please run the full deployment script first."
        exit 1
    fi
    
    log_success "Found CloudFront distribution: $distribution_id"
    echo "$distribution_id"
}

# Build the frontend applications (admin and embed)
build_frontend() {
    log_info "Building frontend applications..."

    # Change to project root
    cd "$PROJECT_ROOT" || {
        log_error "Could not change to project root: $PROJECT_ROOT"
        exit 1
    }

    if [ ! -f "package.json" ]; then
        log_error "package.json not found. Make sure you're running this script from the project root."
        exit 1
    fi

    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing dependencies..."
        npm install
    fi

    # Build admin package
    log_info "Building admin package..."
    npm run build:admin

    if [ ! -d "$ADMIN_BUILD_DIR" ]; then
        log_error "Admin build directory '$ADMIN_BUILD_DIR' not found after build."
        exit 1
    fi

    # Build embed package
    log_info "Building embed package..."
    npm run build:embed

    if [ ! -d "$EMBED_BUILD_DIR" ]; then
        log_error "Embed build directory '$EMBED_BUILD_DIR' not found after build."
        exit 1
    fi

    log_success "Frontend applications built successfully"
}

# Upload files to S3
upload_to_s3() {
    log_info "Uploading files to S3 bucket '$BUCKET_NAME'..."

    # Upload admin files to /admin/ prefix
    log_info "Uploading admin application..."
    aws s3 sync "$ADMIN_BUILD_DIR/" "s3://$BUCKET_NAME/admin/" \
        --delete \
        --cache-control "${STATIC_CACHE_CONTROL:-public, max-age=31536000}" \
        --exclude "*.html" \
        --exclude "*.json"

    # Upload admin HTML and JSON files with shorter cache control
    aws s3 sync "$ADMIN_BUILD_DIR/" "s3://$BUCKET_NAME/admin/" \
        --cache-control "${HTML_CACHE_CONTROL:-public, max-age=0, must-revalidate}" \
        --include "*.html" \
        --include "*.json"

    # Upload embed files to /embed/ prefix
    log_info "Uploading embed widget..."
    aws s3 sync "$EMBED_BUILD_DIR/" "s3://$BUCKET_NAME/embed/" \
        --delete \
        --cache-control "${STATIC_CACHE_CONTROL:-public, max-age=31536000}" \
        --exclude "*.html" \
        --exclude "*.json"

    # Upload embed HTML and JSON files with shorter cache control
    aws s3 sync "$EMBED_BUILD_DIR/" "s3://$BUCKET_NAME/embed/" \
        --cache-control "${HTML_CACHE_CONTROL:-public, max-age=0, must-revalidate}" \
        --include "*.html" \
        --include "*.json"

    log_success "Files uploaded to S3 successfully"
}

# Invalidate CloudFront cache
invalidate_cloudfront() {
    local distribution_id=$1
    log_info "Creating CloudFront invalidation..."
    
    local invalidation_id=$(aws cloudfront create-invalidation \
        --distribution-id "$distribution_id" \
        --paths "/*" \
        --query 'Invalidation.Id' --output text)
    
    log_success "CloudFront invalidation created: $invalidation_id"
    log_info "Cache invalidation may take a few minutes to complete."
}

# Get CloudFront domain name
get_cloudfront_domain() {
    local distribution_id=$1
    local domain_name=$(aws cloudfront get-distribution \
        --id "$distribution_id" \
        --query 'Distribution.DomainName' --output text)
    
    echo "$domain_name"
}

# Main update function
main() {
    log_info "Starting frontend update deployment..."
    log_info "Bucket: $BUCKET_NAME"
    echo
    
    # Check prerequisites
    check_aws_cli
    check_s3_bucket
    
    # Get distribution ID
    local distribution_id=$(get_distribution_id)
    
    # Build and deploy
    build_frontend
    upload_to_s3
    invalidate_cloudfront "$distribution_id"
    
    # Get results
    local cloudfront_domain=$(get_cloudfront_domain "$distribution_id")
    
    echo
    log_success "Update deployment completed successfully!"
    echo
    echo "=== DEPLOYMENT INFORMATION ==="
    echo "S3 Bucket: $BUCKET_NAME"
    echo "CloudFront Distribution ID: $distribution_id"
    echo "CloudFront Domain: $cloudfront_domain"
    echo
    echo "=== APPLICATION URLS ==="
    echo "Admin Panel: https://$cloudfront_domain/admin/"
    echo "Embed Widget: https://$cloudfront_domain/embed/"
    echo "Root (redirects to admin): https://$cloudfront_domain/"
    echo
    log_info "Your changes should be live within a few minutes after cache invalidation completes."
}

# Run main function
main "$@"
