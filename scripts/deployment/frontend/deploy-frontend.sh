#!/bin/bash

# AWS Frontend Deployment Script
# This script creates S3 bucket, CloudFront distribution, builds and deploys the frontend

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
BUCKET_NAME="${BUCKET_NAME:-quote-gen.bloodandtreasure.com}"
REGION="${AWS_REGION:-us-east-1}"
BUILD_DIR="${BUILD_DIR:-dist}"
CLOUDFRONT_COMMENT="${CLOUDFRONT_COMMENT:-Frontend Distribution for $BUCKET_NAME}"

# Note: Colors and logging functions are now provided by global.sh

# Note: check_aws_cli function is now provided by global.sh

# Create S3 bucket if it doesn't exist
create_s3_bucket() {
    log_info "Checking if S3 bucket '$BUCKET_NAME' exists..."
    
    if aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
        log_success "S3 bucket '$BUCKET_NAME' already exists"
    else
        log_info "Creating S3 bucket '$BUCKET_NAME'..."
        
        if [ "$REGION" = "us-east-1" ]; then
            aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION"
        else
            aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION" \
                --create-bucket-configuration LocationConstraint="$REGION"
        fi
        
        log_success "S3 bucket '$BUCKET_NAME' created successfully"
    fi
    
    # Configure bucket settings
    configure_s3_bucket
}

# Configure S3 bucket settings
configure_s3_bucket() {
    log_info "Configuring S3 bucket settings..."
    
    # Disable ACLs (use bucket policies instead)
    log_info "Disabling S3 bucket ACLs..."
    aws s3api put-public-access-block \
        --bucket "$BUCKET_NAME" \
        --public-access-block-configuration \
        "BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=false,RestrictPublicBuckets=false"
    
    # Disable bucket ACLs
    aws s3api put-bucket-ownership-controls \
        --bucket "$BUCKET_NAME" \
        --ownership-controls Rules='[{ObjectOwnership=BucketOwnerEnforced}]'
    
    # Configure static website hosting
    log_info "Configuring static website hosting..."
    aws s3api put-bucket-website \
        --bucket "$BUCKET_NAME" \
        --website-configuration '{
            "IndexDocument": {"Suffix": "index.html"},
            "ErrorDocument": {"Key": "index.html"}
        }'
    
    log_success "S3 bucket configured successfully"
}

# Get or create CloudFront Origin Access Control
get_or_create_oac() {
    log_info "Checking for existing Origin Access Control..."
    
    local oac_id=$(aws cloudfront list-origin-access-controls \
        --query "OriginAccessControlList.Items[?Name=='$BUCKET_NAME-OAC'].Id" \
        --output text)
    
    if [ -n "$oac_id" ] && [ "$oac_id" != "None" ]; then
        log_success "Found existing Origin Access Control: $oac_id"
        echo "$oac_id"
    else
        log_info "Creating new Origin Access Control..."
        local oac_config='{
            "Name": "'$BUCKET_NAME'-OAC",
            "Description": "Origin Access Control for '$BUCKET_NAME'",
            "OriginAccessControlOriginType": "s3",
            "SigningBehavior": "always",
            "SigningProtocol": "sigv4"
        }'
        
        local new_oac_id=$(aws cloudfront create-origin-access-control \
            --origin-access-control-config "$oac_config" \
            --query 'OriginAccessControl.Id' --output text)
        
        log_success "Created new Origin Access Control: $new_oac_id"
        echo "$new_oac_id"
    fi
}

# Create CloudFront distribution if it doesn't exist
create_cloudfront_distribution() {
    log_info "Checking for existing CloudFront distribution..." >&2

    local distribution_id=$(aws cloudfront list-distributions \
        --query "DistributionList.Items[?Comment=='$CLOUDFRONT_COMMENT'].Id" \
        --output text)

    if [ -n "$distribution_id" ] && [ "$distribution_id" != "None" ]; then
        log_success "Found existing CloudFront distribution: $distribution_id" >&2
        echo "$distribution_id"
        return
    fi

    log_info "Creating CloudFront distribution..." >&2
    
    # Get Origin Access Control ID
    local oac_id=$(get_or_create_oac)
    local caller_reference=$(date +%s)

    # Create distribution configuration
    local distribution_config=$(cat <<EOF
{
    "CallerReference": "${caller_reference}",
    "Comment": "${CLOUDFRONT_COMMENT}",
    "DefaultRootObject": "index.html",
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "${BUCKET_NAME}-origin",
                "DomainName": "${BUCKET_NAME}.s3.${REGION}.amazonaws.com",
                "S3OriginConfig": {
                    "OriginAccessIdentity": ""
                },
                "OriginAccessControlId": "${oac_id}"
            }
        ]
    },
    "DefaultCacheBehavior": {
        "TargetOriginId": "${BUCKET_NAME}-origin",
        "ViewerProtocolPolicy": "redirect-to-https",
        "TrustedSigners": {
            "Enabled": false,
            "Quantity": 0
        },
        "ForwardedValues": {
            "QueryString": false,
            "Cookies": {"Forward": "none"}
        },
        "MinTTL": 0,
        "DefaultTTL": 86400,
        "MaxTTL": 31536000,
        "Compress": true
    },
    "CustomErrorResponses": {
        "Quantity": 1,
        "Items": [
            {
                "ErrorCode": 404,
                "ResponsePagePath": "/index.html",
                "ResponseCode": "200",
                "ErrorCachingMinTTL": 300
            }
        ]
    },
    "Enabled": true,
    "PriceClass": "PriceClass_100"
}
EOF
)
    
    local new_distribution_id=$(aws cloudfront create-distribution \
        --distribution-config "$distribution_config" \
        --query 'Distribution.Id' --output text)
    
    log_success "Created CloudFront distribution: $new_distribution_id" >&2
    log_info "Distribution is being deployed... This may take 10-15 minutes." >&2
    
    echo "$new_distribution_id"
}

# Update S3 bucket policy to allow CloudFront access
update_bucket_policy() {
    local distribution_id=$1
    log_info "Updating S3 bucket policy for CloudFront access..."

    # Get AWS account ID
    local account_id=$(aws sts get-caller-identity --query Account --output text)

    # Create bucket policy with proper variable substitution
    local policy_file="/tmp/bucket-policy-${BUCKET_NAME}.json"
    cat > "$policy_file" <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowCloudFrontServicePrincipal",
            "Effect": "Allow",
            "Principal": {
                "Service": "cloudfront.amazonaws.com"
            },
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::${BUCKET_NAME}/*",
            "Condition": {
                "StringEquals": {
                    "AWS:SourceArn": "arn:aws:cloudfront::${account_id}:distribution/${distribution_id}"
                }
            }
        }
    ]
}
EOF

    aws s3api put-bucket-policy --bucket "$BUCKET_NAME" --policy "file://$policy_file"

    # Clean up temporary file
    rm -f "$policy_file"
    log_success "S3 bucket policy updated successfully"
}

# Build the frontend applications (admin and embed)
build_frontend() {
    log_info "Building frontend applications..."

    # Change to project root
    cd "$PROJECT_ROOT" || {
        log_error "Could not change to project root: $PROJECT_ROOT"
        exit 1
    }

    if [ ! -f "package.json" ]; then
        log_error "package.json not found. Make sure you're running this script from the project root."
        exit 1
    fi

    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        log_info "Installing dependencies..."
        npm install
    fi

    # Build admin package
    log_info "Building admin package..."
    npm run build:admin

    if [ ! -d "$ADMIN_BUILD_DIR" ]; then
        log_error "Admin build directory '$ADMIN_BUILD_DIR' not found after build."
        exit 1
    fi

    # Build embed package
    log_info "Building embed package..."
    npm run build:embed

    if [ ! -d "$EMBED_BUILD_DIR" ]; then
        log_error "Embed build directory '$EMBED_BUILD_DIR' not found after build."
        exit 1
    fi

    log_success "Frontend applications built successfully"
}

# Upload files to S3
upload_to_s3() {
    log_info "Uploading files to S3 bucket '$BUCKET_NAME'..."

    # Upload admin files to /admin/ prefix
    log_info "Uploading admin application..."
    aws s3 sync "$ADMIN_BUILD_DIR/" "s3://$BUCKET_NAME/admin/" \
        --delete \
        --cache-control "${STATIC_CACHE_CONTROL:-public, max-age=31536000}" \
        --exclude "*.html" \
        --exclude "*.json"

    # Upload admin HTML and JSON files with shorter cache control
    aws s3 sync "$ADMIN_BUILD_DIR/" "s3://$BUCKET_NAME/admin/" \
        --cache-control "${HTML_CACHE_CONTROL:-public, max-age=0, must-revalidate}" \
        --include "*.html" \
        --include "*.json"

    # Upload embed files to /embed/ prefix
    log_info "Uploading embed widget..."
    aws s3 sync "$EMBED_BUILD_DIR/" "s3://$BUCKET_NAME/embed/" \
        --delete \
        --cache-control "${STATIC_CACHE_CONTROL:-public, max-age=31536000}" \
        --exclude "*.html" \
        --exclude "*.json"

    # Upload embed HTML and JSON files with shorter cache control
    aws s3 sync "$EMBED_BUILD_DIR/" "s3://$BUCKET_NAME/embed/" \
        --cache-control "${HTML_CACHE_CONTROL:-public, max-age=0, must-revalidate}" \
        --include "*.html" \
        --include "*.json"

    # Create a simple index.html at root that redirects to admin
    log_info "Creating root index.html..."
    echo '<!DOCTYPE html>
<html>
<head>
    <title>AI Answer Bot Demo</title>
    <meta http-equiv="refresh" content="0; url=/admin/">
</head>
<body>
    <p>Redirecting to <a href="/admin/">Admin Panel</a>...</p>
</body>
</html>' | aws s3 cp - "s3://$BUCKET_NAME/index.html" \
        --cache-control "${HTML_CACHE_CONTROL:-public, max-age=0, must-revalidate}" \
        --content-type "text/html"

    log_success "Files uploaded to S3 successfully"
}

# Invalidate CloudFront cache
invalidate_cloudfront() {
    local distribution_id=$1
    log_info "Creating CloudFront invalidation..."
    
    local invalidation_id=$(aws cloudfront create-invalidation \
        --distribution-id "$distribution_id" \
        --paths "/*" \
        --query 'Invalidation.Id' --output text)
    
    log_success "CloudFront invalidation created: $invalidation_id"
    log_info "Cache invalidation may take a few minutes to complete."
}

# Get CloudFront domain name
get_cloudfront_domain() {
    local distribution_id=$1
    local domain_name=$(aws cloudfront get-distribution \
        --id "$distribution_id" \
        --query 'Distribution.DomainName' --output text)
    
    echo "$domain_name"
}

# Main deployment function
main() {
    log_info "Starting AWS frontend deployment..."
    log_info "Bucket: $BUCKET_NAME"
    log_info "Region: $REGION"
    echo
    
    # Check prerequisites
    check_aws_cli
    
    # Create and configure S3 bucket
    create_s3_bucket
    
    # Create CloudFront distribution
    local distribution_id=$(create_cloudfront_distribution)
    
    # Update bucket policy
    update_bucket_policy "$distribution_id"
    
    # Build and deploy
    build_frontend
    upload_to_s3
    invalidate_cloudfront "$distribution_id"
    
    # Get results
    local cloudfront_domain=$(get_cloudfront_domain "$distribution_id")
    
    echo
    log_success "Deployment completed successfully!"
    echo
    echo "=== DEPLOYMENT INFORMATION ==="
    echo "S3 Bucket: $BUCKET_NAME"
    echo "S3 Region: $REGION"
    echo "CloudFront Distribution ID: $distribution_id"
    echo "CloudFront Domain: $cloudfront_domain"
    echo
    echo "=== APPLICATION URLS ==="
    echo "Admin Panel: https://$cloudfront_domain/admin/"
    echo "Embed Widget: https://$cloudfront_domain/embed/"
    echo "Root (redirects to admin): https://$cloudfront_domain/"
    echo
    log_info "Note: If this is a new CloudFront distribution, it may take 10-15 minutes to fully deploy."
    log_info "You can check the status with: aws cloudfront get-distribution --id $distribution_id"
}

# Run main function
main "$@"
