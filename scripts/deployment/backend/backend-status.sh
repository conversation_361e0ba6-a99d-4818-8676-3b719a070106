#!/bin/bash

# AWS Backend Status Script
# This script checks the status of Lambda function and API Gateway

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-ai-answer-bot-api}"
API_GATEWAY_NAME="${API_GATEWAY_NAME:-ai-answer-bot-api-gateway}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Note: Colors and logging functions are now provided by global.sh

# Check Lambda function status
check_lambda_status() {
    echo "=== LAMBDA FUNCTION STATUS ==="
    
    if aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" &>/dev/null; then
        log_success "Lambda function '$LAMBDA_FUNCTION_NAME' exists"
        
        # Get function details
        local function_info=$(aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME")
        local config_info=$(aws lambda get-function-configuration --function-name "$LAMBDA_FUNCTION_NAME")
        
        echo "Function Name: $(echo "$config_info" | jq -r '.FunctionName')"
        echo "Function ARN: $(echo "$config_info" | jq -r '.FunctionArn')"
        echo "Runtime: $(echo "$config_info" | jq -r '.Runtime')"
        echo "Handler: $(echo "$config_info" | jq -r '.Handler')"
        echo "Memory: $(echo "$config_info" | jq -r '.MemorySize')MB"
        echo "Timeout: $(echo "$config_info" | jq -r '.Timeout')s"
        echo "State: $(echo "$config_info" | jq -r '.State')"
        echo "Last Modified: $(echo "$config_info" | jq -r '.LastModified')"
        echo "Code Size: $(echo "$config_info" | jq -r '.CodeSize') bytes"
        echo "Role: $(echo "$config_info" | jq -r '.Role')"
        
        # Check if function is ready
        local state=$(echo "$config_info" | jq -r '.State')
        if [ "$state" = "Active" ]; then
            log_success "Lambda function is active and ready"
        else
            log_warning "Lambda function state: $state"
        fi
        
        # Get recent invocations (if CloudWatch logs are available)
        echo
        echo "Recent Invocations (last 5 minutes):"
        local log_group="/aws/lambda/$LAMBDA_FUNCTION_NAME"
        
        if aws logs describe-log-groups --log-group-name-prefix "$log_group" &>/dev/null; then
            local end_time=$(date +%s)000
            local start_time=$((end_time - 300000))  # 5 minutes ago
            
            local invocations=$(aws logs filter-log-events \
                --log-group-name "$log_group" \
                --start-time "$start_time" \
                --end-time "$end_time" \
                --filter-pattern "START RequestId" \
                --query 'events[*].message' \
                --output text 2>/dev/null | wc -l || echo "0")
            
            echo "  Invocations: $invocations"
            
            # Check for errors
            local errors=$(aws logs filter-log-events \
                --log-group-name "$log_group" \
                --start-time "$start_time" \
                --end-time "$end_time" \
                --filter-pattern "ERROR" \
                --query 'events[*].message' \
                --output text 2>/dev/null | wc -l || echo "0")
            
            if [ "$errors" -gt 0 ]; then
                log_warning "Recent errors: $errors"
            else
                echo "  Recent errors: 0"
            fi
        else
            echo "  CloudWatch logs not available or not accessible"
        fi
        
    else
        log_error "Lambda function '$LAMBDA_FUNCTION_NAME' not found"
        return 1
    fi
}

# Check API Gateway status
check_api_gateway_status() {
    echo
    echo "=== API GATEWAY STATUS ==="
    
    # Get API Gateway ID
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        log_success "API Gateway '$API_GATEWAY_NAME' exists"
        
        # Get API details
        local api_info=$(aws apigateway get-rest-api --rest-api-id "$api_id")
        
        echo "API ID: $api_id"
        echo "API Name: $(echo "$api_info" | jq -r '.name')"
        echo "Description: $(echo "$api_info" | jq -r '.description // "N/A"')"
        echo "Created Date: $(echo "$api_info" | jq -r '.createdDate')"
        echo "Endpoint Type: $(echo "$api_info" | jq -r '.endpointConfiguration.types[0]')"
        
        # Check stage status
        echo
        echo "Stage Information:"
        if aws apigateway get-stage --rest-api-id "$api_id" --stage-name "$API_GATEWAY_STAGE" &>/dev/null; then
            local stage_info=$(aws apigateway get-stage --rest-api-id "$api_id" --stage-name "$API_GATEWAY_STAGE")
            
            echo "  Stage Name: $API_GATEWAY_STAGE"
            echo "  Deployment ID: $(echo "$stage_info" | jq -r '.deploymentId')"
            echo "  Created Date: $(echo "$stage_info" | jq -r '.createdDate')"
            echo "  Last Updated: $(echo "$stage_info" | jq -r '.lastUpdatedDate')"
            
            # Check caching
            local cache_enabled=$(echo "$stage_info" | jq -r '.cacheClusterEnabled // false')
            echo "  Caching Enabled: $cache_enabled"
            
            if [ "$cache_enabled" = "true" ]; then
                echo "  Cache Size: $(echo "$stage_info" | jq -r '.cacheClusterSize')"
            fi
            
            log_success "Stage '$API_GATEWAY_STAGE' is deployed"
        else
            log_warning "Stage '$API_GATEWAY_STAGE' not found"
        fi
        
        # Get endpoint URL
        local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
        echo
        echo "API Endpoint: $api_endpoint"
        
        # Test endpoint if curl is available
        if command -v curl &> /dev/null; then
            echo
            echo "Endpoint Health Check:"
            
            local health_url="$api_endpoint/api/health"
            echo "  Testing: $health_url"
            
            local response=$(curl -s -w "%{http_code}" -o /tmp/api_response.txt "$health_url" 2>/dev/null || echo "000")
            
            if [ "$response" = "200" ]; then
                log_success "Health check passed (HTTP $response)"
                echo "  Response: $(cat /tmp/api_response.txt 2>/dev/null || echo 'No response body')"
            elif [ "$response" = "000" ]; then
                log_error "Health check failed (Connection error)"
            else
                log_warning "Health check returned HTTP $response"
                echo "  Response: $(cat /tmp/api_response.txt 2>/dev/null || echo 'No response body')"
            fi
            
            rm -f /tmp/api_response.txt
        else
            echo
            log_info "curl not available for endpoint testing"
            echo "Manual test: curl $api_endpoint/api/health"
        fi
        
    else
        log_error "API Gateway '$API_GATEWAY_NAME' not found"
        return 1
    fi
}

# Check IAM permissions
check_iam_permissions() {
    echo
    echo "=== IAM PERMISSIONS ==="
    
    local role_name="${LAMBDA_FUNCTION_NAME}-execution-role"
    
    if aws iam get-role --role-name "$role_name" &>/dev/null; then
        log_success "IAM role '$role_name' exists"
        
        # List attached policies
        echo "Attached Policies:"
        aws iam list-attached-role-policies --role-name "$role_name" \
            --query 'AttachedPolicies[*].PolicyName' \
            --output text | tr '\t' '\n' | sed 's/^/  - /'
        
        # Check Lambda permissions for API Gateway
        echo
        echo "Lambda Permissions:"
        local permissions=$(aws lambda get-policy --function-name "$LAMBDA_FUNCTION_NAME" 2>/dev/null || echo '{"Policy": "{}"}')
        
        if echo "$permissions" | jq -r '.Policy' | jq -e '.Statement[]' &>/dev/null; then
            local api_permissions=$(echo "$permissions" | jq -r '.Policy' | jq -r '.Statement[] | select(.Principal.Service == "apigateway.amazonaws.com") | .Sid')
            
            if [ -n "$api_permissions" ]; then
                log_success "API Gateway invoke permissions configured"
                echo "  Statement IDs: $api_permissions"
            else
                log_warning "No API Gateway invoke permissions found"
            fi
        else
            log_warning "No Lambda resource policy found"
        fi
        
    else
        log_error "IAM role '$role_name' not found"
    fi
}

# Get deployment summary
get_deployment_summary() {
    echo
    echo "=== DEPLOYMENT SUMMARY ==="
    
    local lambda_exists=false
    local api_exists=false
    
    # Check Lambda
    if aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" &>/dev/null; then
        lambda_exists=true
        log_success "Lambda Function: Deployed"
    else
        log_error "Lambda Function: Not Found"
    fi
    
    # Check API Gateway
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        api_exists=true
        log_success "API Gateway: Deployed"
        
        if aws apigateway get-stage --rest-api-id "$api_id" --stage-name "$API_GATEWAY_STAGE" &>/dev/null; then
            log_success "API Stage: Deployed"
        else
            log_warning "API Stage: Not Found"
        fi
    else
        log_error "API Gateway: Not Found"
    fi
    
    echo
    if [ "$lambda_exists" = true ] && [ "$api_exists" = true ]; then
        log_success "Backend deployment is complete and operational"
        
        local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
        echo
        echo "Quick Reference:"
        echo "  API Endpoint: $api_endpoint"
        echo "  Health Check: $api_endpoint/api/health"
        echo "  Lambda Function: $LAMBDA_FUNCTION_NAME"
        echo "  API Gateway: $API_GATEWAY_NAME"
    else
        log_warning "Backend deployment is incomplete"
        echo
        echo "To deploy missing components:"
        if [ "$lambda_exists" = false ]; then
            echo "  Lambda: ./scripts/deploy-backend.sh"
        fi
        if [ "$api_exists" = false ]; then
            echo "  API Gateway: ./scripts/deploy-api-gateway.sh"
        fi
    fi
}

# Main function
main() {
    echo "=== AI ANSWER BOT DEMO - BACKEND STATUS ==="
    log_info "Checking backend deployment status..."
    echo
    
    # Check each component
    check_lambda_status
    check_api_gateway_status
    check_iam_permissions
    get_deployment_summary
}

# Run main function
main "$@"
