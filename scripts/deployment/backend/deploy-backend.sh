#!/bin/bash

# AWS Backend Deployment Script
# This script creates Lambda function, API Gateway, and deploys the backend

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-quote-gen-bloodandtreasure}"
LAMBDA_RUNTIME="${LAMBDA_RUNTIME:-nodejs20.x}"
LAMBDA_MEMORY="${LAMBDA_MEMORY:-512}"
LAMBDA_TIMEOUT="${LAMBDA_TIMEOUT:-30}"
LAMBDA_HANDLER="${LAMBDA_HANDLER:-dist/lambda-handler.handler}"
BACKEND_DIR="${BACKEND_DIR:-backend}"
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"

# Note: Colors, logging functions, and check_aws_cli are now provided by global.sh

# Check if backend directory exists and is valid
check_backend_directory() {
    log_info "Checking backend directory..."

    if [ ! -d "$PROJECT_ROOT/$BACKEND_DIR" ]; then
        log_error "Backend directory '$BACKEND_DIR' not found in project root."
        exit 1
    fi

    if [ ! -f "$PROJECT_ROOT/$BACKEND_DIR/package.json" ]; then
        log_error "package.json not found in backend directory."
        exit 1
    fi

    if [ ! -f "$PROJECT_ROOT/$BACKEND_DIR/src/index.ts" ]; then
        log_error "src/index.ts not found in backend directory."
        exit 1
    fi

    log_success "Backend directory structure validated"
}

# Create or get IAM role for Lambda
create_lambda_role() {
    log_info "Creating/checking Lambda execution role..."
    
    local role_name="${LAMBDA_FUNCTION_NAME}-execution-role"
    local role_arn
    
    # Check if role exists
    if aws iam get-role --role-name "$role_name" &>/dev/null; then
        role_arn=$(aws iam get-role --role-name "$role_name" --query 'Role.Arn' --output text)
        log_success "Found existing IAM role: $role_name"
    else
        log_info "Creating new IAM role: $role_name"
        
        # Create trust policy for Lambda
        local trust_policy=$(cat <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
)
        
        # Create the role
        role_arn=$(aws iam create-role \
            --role-name "$role_name" \
            --assume-role-policy-document "$trust_policy" \
            --description "Execution role for $LAMBDA_FUNCTION_NAME Lambda function" \
            --query 'Role.Arn' --output text)
        
        # Attach basic execution policy
        aws iam attach-role-policy \
            --role-name "$role_name" \
            --policy-arn "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
        
        log_success "Created IAM role: $role_name"
        
        # Wait a moment for role to propagate
        log_info "Waiting for IAM role to propagate..."
        sleep 10
    fi
    
    echo "$role_arn"
}

# Build backend for Lambda deployment
build_backend() {
    log_info "Building backend for Lambda deployment..."

    cd "$PROJECT_ROOT/$BACKEND_DIR"

    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        log_info "Installing all dependencies for building..."
        npm install
    else
        log_info "Dependencies already installed, skipping npm install"
    fi

    # Build TypeScript
    log_info "Building TypeScript..."
    npm run build

    # Install serverless-express for Lambda compatibility
    log_info "Installing serverless-express..."
    npm install @vendia/serverless-express

    # Create Lambda handler wrapper
    log_info "Creating Lambda handler wrapper..."
    cat > dist/index.js << 'EOF'
const serverlessExpress = require('@vendia/serverless-express');
const app = require('./app.js');

// Export Lambda handler
exports.handler = serverlessExpress({ app });
EOF

    # Create app.js that exports the Express app
    log_info "Creating app.js wrapper..."
    cat > dist/app.js << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

// Set production environment for Lambda
process.env.NODE_ENV = 'production';

const app = express();

// Middleware
app.use(helmet());
app.use(cors({
    origin: process.env.CORS_ORIGINS?.split(',') || ['*'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
    credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API routes - you'll need to implement these based on your actual routes
app.get('/api/clients', (req, res) => {
    res.json({ message: 'Clients endpoint - implement your logic here' });
});

app.post('/api/chat', (req, res) => {
    res.json({ message: 'Chat endpoint - implement your logic here' });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

module.exports = app;
EOF

    cd "$PROJECT_ROOT"
    log_success "Backend built successfully"
}

# Create deployment package
create_deployment_package() {
    log_info "Creating deployment package..." >&2

    local temp_dir=$(mktemp -d)
    local package_file="$PROJECT_ROOT/lambda-deployment.zip"

    # Copy built files
    cp -r "$PROJECT_ROOT/$BACKEND_DIR/dist/"* "$temp_dir/"
    cp -r "$PROJECT_ROOT/$BACKEND_DIR/node_modules" "$temp_dir/"
    cp "$PROJECT_ROOT/$BACKEND_DIR/package.json" "$temp_dir/"

    # Create zip package
    cd "$temp_dir"
    zip -r "$package_file" . -x "*.ts" "*.map" "tsconfig.json" > /dev/null

    # Cleanup
    rm -rf "$temp_dir"
    cd "$PROJECT_ROOT"

    log_success "Deployment package created: lambda-deployment.zip" >&2
    echo "$package_file"
}

# Create or update Lambda function
create_lambda_function() {
    local role_arn=$1
    local package_file=$2
    
    log_info "Creating/updating Lambda function..."
    
    # Check if function exists
    if aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" &>/dev/null; then
        log_info "Updating existing Lambda function..."
        
        # Update function code
        aws lambda update-function-code \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --zip-file "fileb://$package_file" > /dev/null
        
        # Update function configuration
        aws lambda update-function-configuration \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --runtime "$LAMBDA_RUNTIME" \
            --handler "$LAMBDA_HANDLER" \
            --memory-size "$LAMBDA_MEMORY" \
            --timeout "$LAMBDA_TIMEOUT" \
            --environment Variables="{NODE_ENV=production}" > /dev/null
        
        log_success "Lambda function updated"
    else
        log_info "Creating new Lambda function..."
        
        aws lambda create-function \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --runtime "$LAMBDA_RUNTIME" \
            --role "$role_arn" \
            --handler "$LAMBDA_HANDLER" \
            --zip-file "fileb://$package_file" \
            --memory-size "$LAMBDA_MEMORY" \
            --timeout "$LAMBDA_TIMEOUT" \
            --environment Variables="{NODE_ENV=production}" \
            --description "Backend API for AI Answer Bot Demo" > /dev/null
        
        log_success "Lambda function created"
    fi
    
    # Wait for function to be ready
    log_info "Waiting for Lambda function to be ready..."
    aws lambda wait function-updated --function-name "$LAMBDA_FUNCTION_NAME"
    
    # Get function ARN
    local function_arn=$(aws lambda get-function \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --query 'Configuration.FunctionArn' --output text)
    
    echo "$function_arn"
}

# Main deployment function
main() {
    log_info "Starting AWS backend deployment..."
    log_info "Lambda Function: $LAMBDA_FUNCTION_NAME"
    log_info "API Gateway: $API_GATEWAY_NAME"
    log_info "Region: $AWS_REGION"
    echo

    # Check prerequisites
    check_aws_cli
    check_backend_directory

    # Create IAM role
    local role_arn=$(create_lambda_role)

    # Build and package backend
    build_backend
    local package_file=$(create_deployment_package)

    # Create/update Lambda function
    local function_arn=$(create_lambda_function "$role_arn" "$package_file")

    # Cleanup deployment package
    rm -f "$package_file"

    echo
    log_success "Lambda deployment completed successfully!"
    echo
    echo "=== LAMBDA INFORMATION ==="
    echo "Lambda Function Name: $LAMBDA_FUNCTION_NAME"
    echo "Lambda Function ARN: $function_arn"
    echo "IAM Role ARN: $role_arn"
    echo "Runtime: $LAMBDA_RUNTIME"
    echo "Memory: ${LAMBDA_MEMORY}MB"
    echo "Timeout: ${LAMBDA_TIMEOUT}s"
    echo

    # Deploy API Gateway
    log_info "Deploying API Gateway..."
    "$SCRIPT_DIR/deploy-api-gateway.sh"

    echo
    log_success "Complete backend deployment finished successfully!"
}

# Run main function
main "$@"
