# Lambda Log Tailing

This directory contains scripts for tailing AWS Lambda logs in real-time.

## Quick Start

### Using npm scripts (Recommended)
```bash
# Tail logs from the last 10 minutes (default)
npm run logs

# Show recent logs and exit
npm run logs:recent

# Only show ERROR logs
npm run logs:errors

# List available log streams
npm run logs:streams
```

### Using the script directly
```bash
# Basic usage - tail logs from last 10 minutes
./scripts/tail-logs.sh

# Tail logs from last hour
./scripts/tail-logs.sh -f 1h

# Show only ERROR logs
./scripts/tail-logs.sh -p 'ERROR'

# Show recent logs and exit
./scripts/tail-logs.sh -r
```

## Features

- **Real-time tailing**: Continuously monitors Lambda logs using AWS CLI `logs tail`
- **Time-based filtering**: Start from specific time (e.g., 1h, 30m, 5s ago)
- **Pattern filtering**: Filter logs using CloudWatch Logs filter patterns
- **Stream listing**: View available log streams
- **Graceful handling**: Handles missing log groups and Ctrl+C interruption
- **Actual log content**: Shows the real log messages, not just file names

## Command Options

| Option | Description | Example |
|--------|-------------|---------|
| `-f, --follow <time>` | Start tailing from time ago | `-f 1h`, `-f 30m` |
| `-p, --pattern <filter>` | Filter logs with pattern | `-p 'ERROR'`, `-p 'Chat API'` |
| `-i, --interval <sec>` | Polling interval (default: 2s) | `-i 5` |
| `-r, --recent` | Show recent logs and exit | `-r` |
| `-s, --streams` | List available log streams | `-s` |
| `-h, --help` | Show help message | `-h` |

## Examples

```bash
# Tail all logs from the last hour
npm run logs -- -f 1h

# Show only logs containing "ERROR" from last 30 minutes
./scripts/tail-logs.sh -f 30m -p 'ERROR'

# Show logs with custom polling interval
./scripts/tail-logs.sh -i 5

# List recent log streams to see activity
npm run logs:streams
```

## What You'll See

The logs show real Lambda execution details including:
- **Request lifecycle**: START, END, and REPORT messages
- **Application logs**: Your custom log messages (INFO, WARN, ERROR)
- **Performance metrics**: Duration, memory usage, billing info
- **Conversation analysis**: Your AI chatbot's conversation state tracking
- **Error details**: Stack traces and error messages when they occur

## Filter Patterns

CloudWatch Logs supports powerful filter patterns:

```bash
# Simple text search
-p 'ERROR'

# Multiple terms (OR)
-p 'ERROR WARN'

# Structured JSON filtering
-p '[timestamp, request_id, "ERROR"]'

# Field-based filtering
-p '{ $.level = "ERROR" }'
```

## Configuration

The script automatically uses your project configuration:
- **Lambda Function**: `quote-gen-bloodandtreasure`
- **AWS Region**: `us-east-1`
- **Log Group**: `/aws/lambda/quote-gen-bloodandtreasure`

## Troubleshooting

### "Log group not found"
This usually means the Lambda function hasn't been invoked yet. The log group is created on first invocation.

### "Lambda function not found"
Check that:
1. AWS credentials are configured (`aws configure`)
2. You have the correct permissions
3. The Lambda function exists in the specified region

### No recent logs
If you don't see recent logs, try:
1. Check if the Lambda function has been invoked recently
2. Use `-s` to list log streams and verify activity
3. Increase the follow time with `-f 1h` or `-f 1d`

## Integration

The log tailing script integrates with your existing deployment workflow:
- Uses the same AWS credentials as deployment scripts
- Reads configuration from `scripts/deployment/shared/config.sh`
- Follows the same logging patterns as other scripts

## Files Created

- `scripts/connect/backend/tail-lambda-logs.sh` - Main log tailing script
- `scripts/tail-logs.sh` - Convenient wrapper script
- `scripts/connect/backend/README-logs.md` - This documentation

## npm Scripts Added

- `npm run logs` - Tail logs from last 10 minutes
- `npm run logs:recent` - Show recent logs and exit
- `npm run logs:errors` - Show only ERROR logs
- `npm run logs:streams` - List available log streams
