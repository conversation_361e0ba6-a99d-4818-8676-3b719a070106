#!/bin/bash

# Lambda Environment Variables Update Script
# This script updates the Lambda function environment variables from the local .env file

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../../deployment/shared/global.sh"

# Use config variables with fallbacks
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-answer-bot-bloodandtreasure-api}"
API_GATEWAY_NAME="${API_GATEWAY_NAME:-answer-bot-bloodandtreasure-api-gateway}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"
BACKEND_DIR="${BACKEND_DIR:-packages/api}"

# Check if Lambda function exists
check_lambda_exists() {
    log_info "Checking if Lambda function exists..."
    
    if ! resource_exists "lambda-function" "$LAMBDA_FUNCTION_NAME" "$AWS_REGION"; then
        log_error "Lambda function '$LAMBDA_FUNCTION_NAME' not found."
        log_error "Please deploy the backend first: npm run deploy:backend"
        exit 1
    fi
    
    log_success "Lambda function found: $LAMBDA_FUNCTION_NAME"
}

# Read environment variables from .env file
read_env_file() {
    local env_file="$PROJECT_ROOT/$BACKEND_DIR/.env"
    
    log_info "Reading environment variables from .env file..."
    
    if [ ! -f "$env_file" ]; then
        log_error ".env file not found at: $env_file"
        exit 1
    fi
    
    # Read .env file and create JSON for AWS Lambda
    local env_vars="{"
    local first=true
    
    while IFS='=' read -r key value || [ -n "$key" ]; do
        # Skip empty lines and comments
        if [[ -z "$key" || "$key" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # Remove leading/trailing whitespace
        key=$(echo "$key" | xargs)
        value=$(echo "$value" | xargs)
        
        # Skip if key is empty
        if [[ -z "$key" ]]; then
            continue
        fi
        
        # Remove quotes from value if present
        if [[ "$value" =~ ^\".*\"$ ]]; then
            value="${value:1:-1}"
        elif [[ "$value" =~ ^\'.*\'$ ]]; then
            value="${value:1:-1}"
        fi
        
        # Add to JSON (escape quotes in value)
        value=$(echo "$value" | sed 's/"/\\"/g')
        
        if [ "$first" = true ]; then
            env_vars="$env_vars\"$key\":\"$value\""
            first=false
        else
            env_vars="$env_vars,\"$key\":\"$value\""
        fi
        
        log_info "Found environment variable: $key"
        
    done < "$env_file"
    
    # Always add NODE_ENV=production for Lambda
    if [ "$first" = true ]; then
        env_vars="$env_vars\"NODE_ENV\":\"production\""
    else
        env_vars="$env_vars,\"NODE_ENV\":\"production\""
    fi
    
    env_vars="$env_vars}"
    
    echo "$env_vars"
}

# Update Lambda environment variables
update_lambda_env() {
    local env_vars="$1"
    
    log_info "Updating Lambda function environment variables..."
    
    # Update the Lambda function configuration
    aws lambda update-function-configuration \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --environment "Variables=$env_vars" > /dev/null
    
    if [ $? -eq 0 ]; then
        log_success "Environment variables updated successfully"
    else
        log_error "Failed to update environment variables"
        exit 1
    fi
    
    # Wait for function to be updated
    log_info "Waiting for Lambda function to be updated..."
    wait_for_resource "lambda-function" "$LAMBDA_FUNCTION_NAME" 300 10
    
    log_success "Lambda function is ready with new environment variables"
}

# Show current environment variables
show_current_env() {
    log_info "Current Lambda environment variables:"
    
    local current_env=$(aws lambda get-function-configuration \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --query 'Environment.Variables' \
        --output json 2>/dev/null)
    
    if [ $? -eq 0 ] && [ "$current_env" != "null" ]; then
        echo "$current_env" | jq -r 'to_entries[] | "  \(.key) = \(.value)"' 2>/dev/null || echo "$current_env"
    else
        log_warning "No environment variables found or unable to retrieve them"
    fi
}

# Test the updated function
test_function() {
    log_info "Testing updated Lambda function..."
    
    # Get API Gateway endpoint
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
        
        log_info "Testing health endpoint: $api_endpoint/api/health"
        
        # Test with curl if available
        if command -v curl &> /dev/null; then
            local response=$(curl -s -w "%{http_code}" -o /tmp/lambda_test_response.txt "$api_endpoint/api/health" 2>/dev/null || echo "000")
            
            if [ "$response" = "200" ]; then
                log_success "Health check passed (HTTP $response)"
                echo "  Response: $(cat /tmp/lambda_test_response.txt 2>/dev/null || echo 'No response body')"
            else
                log_warning "Health check returned HTTP $response"
                echo "  Response: $(cat /tmp/lambda_test_response.txt 2>/dev/null || echo 'No response body')"
            fi
            
            rm -f /tmp/lambda_test_response.txt
        else
            log_info "curl not available, skipping automatic test"
            log_info "Manual test: curl $api_endpoint/api/health"
        fi
        
        echo
        log_info "API Endpoint: $api_endpoint"
        log_info "You can now test your chat endpoint at: $api_endpoint/api/chat"
    else
        log_warning "API Gateway not found, skipping endpoint test"
    fi
}

# Main function
main() {
    echo "🔧 Lambda Environment Variables Update"
    echo "====================================="
    echo "Lambda Function: $LAMBDA_FUNCTION_NAME"
    echo "Backend Directory: $BACKEND_DIR"
    echo
    
    # Check prerequisites
    check_aws_cli
    check_lambda_exists
    
    # Show current environment variables
    show_current_env
    echo
    
    # Read environment variables from .env file
    local env_vars=$(read_env_file)
    
    echo
    log_info "Environment variables to be set:"
    echo "$env_vars" | jq -r 'to_entries[] | "  \(.key) = \(.value)"' 2>/dev/null || echo "$env_vars"
    echo
    
    # Ask for confirmation
    read -p "Do you want to update the Lambda function with these environment variables? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Update Lambda environment variables
        update_lambda_env "$env_vars"
        
        echo
        # Test the function
        test_function
        
        echo
        log_success "Lambda environment variables updated successfully!"
        log_info "Your backend should now have access to all environment variables from .env file"
    else
        log_info "Update cancelled by user"
    fi
}

# Run main function
main "$@"
