#!/bin/bash

# Lambda Log Tailing Script
# This script allows you to tail AWS Lambda logs in real-time

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../../deployment/shared/global.sh"

# Use config variables with fallbacks
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-answer-bot-bloodandtreasure-api}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Default configuration
DEFAULT_FOLLOW_TIME="10m"  # How far back to start tailing
DEFAULT_FILTER_PATTERN=""  # Empty means show all logs
POLL_INTERVAL=2  # Seconds between polls

# Function to show usage
show_usage() {
    echo "🔍 Lambda Log Tailing Script"
    echo
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  -f, --follow <time>     Start tailing from this time ago (default: $DEFAULT_FOLLOW_TIME)"
    echo "                          Examples: 1h, 30m, 5s, 2024-01-01T10:00:00"
    echo "  -p, --pattern <filter>  Filter logs with CloudWatch Logs filter pattern"
    echo "                          Examples: 'ERROR', '[timestamp, request_id, \"ERROR\"]'"
    echo "  -i, --interval <sec>    Polling interval in seconds (default: $POLL_INTERVAL)"
    echo "  -g, --group <name>      Specify log group name (auto-detected by default)"
    echo "  -s, --streams           List available log streams"
    echo "  -r, --recent            Show recent logs (last 10 minutes) and exit"
    echo "  -h, --help              Show this help message"
    echo
    echo "Examples:"
    echo "  $0                                    # Tail logs from last 10 minutes"
    echo "  $0 -f 1h                             # Tail logs from last hour"
    echo "  $0 -p 'ERROR'                        # Only show ERROR logs"
    echo "  $0 -p '[timestamp, request_id, \"ERROR\"]'  # Structured filter"
    echo "  $0 -r                                # Show recent logs and exit"
    echo "  $0 -s                                # List log streams"
    echo
    echo "Current Configuration:"
    echo "  Lambda Function: $LAMBDA_FUNCTION_NAME"
    echo "  AWS Region: $AWS_REGION"
    echo "  Log Group: /aws/lambda/$LAMBDA_FUNCTION_NAME"
}

# Function to get log group name
get_log_group_name() {
    echo "/aws/lambda/$LAMBDA_FUNCTION_NAME"
}

# Function to check if Lambda function exists
check_lambda_function() {
    log_info "Checking if Lambda function exists..."
    
    if ! check_aws_cli; then
        return 1
    fi
    
    if aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --region "$AWS_REGION" &>/dev/null; then
        log_success "Lambda function '$LAMBDA_FUNCTION_NAME' found"
        return 0
    else
        log_error "Lambda function '$LAMBDA_FUNCTION_NAME' not found in region '$AWS_REGION'"
        log_info "Available functions:"
        aws lambda list-functions --region "$AWS_REGION" --query 'Functions[].FunctionName' --output table 2>/dev/null || echo "  Unable to list functions"
        return 1
    fi
}

# Function to check if log group exists
check_log_group() {
    local log_group_name=$1
    
    if aws logs describe-log-groups --log-group-name-prefix "$log_group_name" --region "$AWS_REGION" --query 'logGroups[0].logGroupName' --output text 2>/dev/null | grep -q "$log_group_name"; then
        return 0
    else
        return 1
    fi
}

# Function to list log streams
list_log_streams() {
    local log_group_name=$(get_log_group_name)
    
    log_info "Listing log streams for $log_group_name..."
    
    if ! check_log_group "$log_group_name"; then
        log_error "Log group '$log_group_name' not found"
        log_info "This usually means the Lambda function hasn't been invoked yet"
        return 1
    fi
    
    echo
    echo "Recent log streams:"
    aws logs describe-log-streams \
        --log-group-name "$log_group_name" \
        --region "$AWS_REGION" \
        --order-by LastEventTime \
        --descending \
        --max-items 10 \
        --query 'logStreams[].[logStreamName,lastEventTime,lastIngestionTime]' \
        --output table
}

# Function to show recent logs
show_recent_logs() {
    local log_group_name=$(get_log_group_name)
    local start_time=$1
    local filter_pattern=$2

    log_info "Fetching recent logs from $log_group_name..."

    if ! check_log_group "$log_group_name"; then
        log_error "Log group '$log_group_name' not found"
        log_info "This usually means the Lambda function hasn't been invoked yet"
        return 1
    fi

    # Build the AWS logs tail command for recent logs (no follow)
    local aws_cmd="aws logs tail '$log_group_name' --region '$AWS_REGION'"

    # Add start time if specified
    if [ -n "$start_time" ]; then
        # Convert milliseconds to seconds for the --since parameter
        local start_time_seconds=$((start_time / 1000))
        local current_time=$(date +%s)
        local seconds_ago=$((current_time - start_time_seconds))

        if [ $seconds_ago -gt 0 ]; then
            aws_cmd="$aws_cmd --since ${seconds_ago}s"
        fi
    else
        # Default to last 10 minutes if no start time specified
        aws_cmd="$aws_cmd --since 10m"
    fi

    # Add filter pattern if specified
    if [ -n "$filter_pattern" ]; then
        aws_cmd="$aws_cmd --filter-pattern '$filter_pattern'"
    fi

    echo
    echo "Recent logs:"
    echo "============"
    eval $aws_cmd
}

# Function to convert time string to epoch milliseconds
time_to_epoch_ms() {
    local time_str=$1
    
    # If it's already a timestamp, return as-is
    if [[ "$time_str" =~ ^[0-9]+$ ]]; then
        echo "$time_str"
        return
    fi
    
    # If it's an ISO timestamp, convert it
    if [[ "$time_str" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2} ]]; then
        date -d "$time_str" +%s000 2>/dev/null || echo ""
        return
    fi
    
    # Handle relative time (e.g., 10m, 1h, 30s)
    local current_time=$(date +%s)
    local seconds_ago=0
    
    if [[ "$time_str" =~ ^([0-9]+)([smhd])$ ]]; then
        local number="${BASH_REMATCH[1]}"
        local unit="${BASH_REMATCH[2]}"
        
        case $unit in
            s) seconds_ago=$number ;;
            m) seconds_ago=$((number * 60)) ;;
            h) seconds_ago=$((number * 3600)) ;;
            d) seconds_ago=$((number * 86400)) ;;
        esac
        
        echo $(((current_time - seconds_ago) * 1000))
    else
        echo ""
    fi
}

# Function to tail logs in real-time using AWS CLI logs tail command
tail_logs() {
    local log_group_name=$(get_log_group_name)
    local start_time=$1
    local filter_pattern=$2
    local poll_interval=$3

    log_info "Starting to tail logs from $log_group_name..."
    log_info "Press Ctrl+C to stop"
    echo

    if ! check_log_group "$log_group_name"; then
        log_warning "Log group '$log_group_name' not found"
        log_info "Waiting for Lambda function to be invoked..."
        echo
    fi

    # Build the AWS logs tail command
    local aws_cmd="aws logs tail '$log_group_name' --region '$AWS_REGION' --follow"

    # Add start time if specified
    if [ -n "$start_time" ]; then
        # Convert milliseconds to seconds for the --since parameter
        local start_time_seconds=$((start_time / 1000))
        local current_time=$(date +%s)
        local seconds_ago=$((current_time - start_time_seconds))

        if [ $seconds_ago -gt 0 ]; then
            aws_cmd="$aws_cmd --since ${seconds_ago}s"
        fi
    fi

    # Add filter pattern if specified
    if [ -n "$filter_pattern" ]; then
        aws_cmd="$aws_cmd --filter-pattern '$filter_pattern'"
    fi

    # Execute the tail command
    echo "Executing: $aws_cmd"
    echo "----------------------------------------"
    eval $aws_cmd
}

# Main function
main() {
    local follow_time="$DEFAULT_FOLLOW_TIME"
    local filter_pattern="$DEFAULT_FILTER_PATTERN"
    local poll_interval="$POLL_INTERVAL"
    local log_group_name=""
    local show_streams=false
    local show_recent=false

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow)
                follow_time="$2"
                shift 2
                ;;
            -p|--pattern)
                filter_pattern="$2"
                shift 2
                ;;
            -i|--interval)
                poll_interval="$2"
                shift 2
                ;;
            -g|--group)
                log_group_name="$2"
                shift 2
                ;;
            -s|--streams)
                show_streams=true
                shift
                ;;
            -r|--recent)
                show_recent=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Check prerequisites
    if ! check_lambda_function; then
        exit 1
    fi

    # Use default log group if not specified
    if [ -z "$log_group_name" ]; then
        log_group_name=$(get_log_group_name)
    fi

    # Handle different modes
    if [ "$show_streams" = true ]; then
        list_log_streams
        exit 0
    fi

    # Convert follow time to epoch milliseconds
    local start_time_ms=""
    if [ -n "$follow_time" ]; then
        start_time_ms=$(time_to_epoch_ms "$follow_time")
        if [ -z "$start_time_ms" ]; then
            log_error "Invalid time format: $follow_time"
            log_info "Use formats like: 10m, 1h, 30s, or 2024-01-01T10:00:00"
            exit 1
        fi
    fi

    if [ "$show_recent" = true ]; then
        show_recent_logs "$start_time_ms" "$filter_pattern"
        exit 0
    fi

    # Start tailing logs
    echo "🔍 Tailing Lambda logs for: $LAMBDA_FUNCTION_NAME"
    echo "📍 Log Group: $log_group_name"
    echo "🕐 Starting from: $follow_time ago"
    if [ -n "$filter_pattern" ]; then
        echo "🔍 Filter: $filter_pattern"
    fi
    echo "⏱️  Poll interval: ${poll_interval}s"
    echo

    tail_logs "$start_time_ms" "$filter_pattern" "$poll_interval"
}

# Handle Ctrl+C gracefully
trap 'echo -e "\n\n👋 Log tailing stopped"; exit 0' INT

# Run main function with all arguments
main "$@"
