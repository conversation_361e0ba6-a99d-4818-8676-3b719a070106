# Deployment Scripts

This directory contains AWS deployment scripts for the AI Answer Bot Demo application.

## 🚀 Quick Start

```bash
# Deploy everything
npm run deploy:full

# Deploy frontend only (admin panel + embed widget)
npm run deploy:frontend

# Deploy backend only (API)
npm run deploy:backend

# Check status
npm run status:all

# Quick updates (faster than full deployment)
npm run deploy:frontend:update
npm run deploy:backend:update
```

## 📁 Directory Structure

```
scripts/
├── README.md                           # This file
├── deployment/                         # New organized structure
│   ├── README.md                       # Deployment documentation
│   ├── frontend/                       # Frontend scripts
│   │   ├── README.md                   # Frontend documentation
│   │   ├── deploy-frontend.sh          # Full frontend deployment
│   │   ├── update-frontend.sh          # Quick frontend updates
│   │   └── frontend-status.sh          # Frontend status check
│   ├── backend/                        # Backend scripts
│   │   ├── README.md                   # Backend documentation
│   │   ├── deploy-backend.sh           # Full backend deployment
│   │   ├── deploy-api-gateway.sh       # API Gateway deployment
│   │   ├── update-backend.sh           # Quick backend updates
│   │   └── backend-status.sh           # Backend status check
│   └── shared/                         # Shared utilities
│       ├── README.md                   # Shared utilities documentation
│       ├── global.sh                   # Common functions
│       └── config.sh                   # Configuration variables
└── [legacy wrapper scripts]            # Backward compatibility
```

## 🔄 Migration Notice

**Scripts have been reorganized!** The new structure provides:

- ✅ **Better Organization**: Scripts grouped by function (frontend/backend/shared)
- ✅ **Comprehensive Documentation**: README files for each component
- ✅ **Shared Utilities**: Common functions in `global.sh` eliminate code duplication
- ✅ **Backward Compatibility**: Old script paths still work via wrapper scripts

### New Locations

| Old Path | New Path |
|----------|----------|
| `scripts/deploy-frontend.sh` | `scripts/deployment/frontend/deploy-frontend.sh` |
| `scripts/deploy-backend.sh` | `scripts/deployment/backend/deploy-backend.sh` |
| `scripts/update-frontend.sh` | `scripts/deployment/frontend/update-frontend.sh` |
| `scripts/update-backend.sh` | `scripts/deployment/backend/update-backend.sh` |
| `scripts/frontend-status.sh` | `scripts/deployment/frontend/frontend-status.sh` |
| `scripts/backend-status.sh` | `scripts/deployment/backend/backend-status.sh` |
| `scripts/global.sh` | `scripts/deployment/shared/global.sh` |
| `scripts/config.sh` | `scripts/deployment/shared/config.sh` |

## 📖 Documentation

Each directory contains detailed documentation:

- **[deployment/README.md](deployment/README.md)** - Overall deployment architecture
- **[deployment/frontend/README.md](deployment/frontend/README.md)** - Frontend deployment details
- **[deployment/backend/README.md](deployment/backend/README.md)** - Backend deployment details  
- **[deployment/shared/README.md](deployment/shared/README.md)** - Shared utilities documentation

## 🛠️ Available Commands

All npm scripts have been updated for the AI Answer Bot Demo project:

```bash
# Frontend (Admin Panel + Embed Widget)
npm run deploy:frontend         # Full frontend deployment to S3/CloudFront
npm run deploy:frontend:update  # Quick frontend updates
npm run deploy:frontend:status  # Frontend status check

# Backend (API)
npm run deploy:backend          # Full backend deployment to Lambda/API Gateway
npm run deploy:backend:update   # Quick backend updates
npm run deploy:backend:status   # Backend status check

# Combined
npm run deploy:full            # Deploy both frontend and backend
npm run status:all             # Check status of all deployments
```

## 🏗️ Project Structure

This is a monorepo with the following packages:

- **`packages/admin`** - React admin panel with Tailwind CSS
- **`packages/embed`** - React embed widget for website integration
- **`packages/api`** - Node.js/TypeScript backend with Express and PostgreSQL

### Deployment Architecture

- **Frontend**: Both admin and embed packages are built and deployed to S3 with CloudFront
  - Admin panel: `https://your-domain.com/admin/`
  - Embed widget: `https://your-domain.com/embed/`
- **Backend**: API package is deployed as AWS Lambda with API Gateway

## 🔧 Key Improvements

1. **Organized Structure**: Scripts logically grouped by function
2. **Shared Utilities**: Common functions in `global.sh` eliminate ~200 lines of duplicate code
3. **Better Documentation**: Comprehensive README files for each component
4. **Backward Compatibility**: Old script paths still work
5. **Consistent Error Handling**: Standardized across all scripts
6. **Easier Maintenance**: Single source of truth for common functionality

## 🚨 Legacy Support

The old script paths in the root `scripts/` directory still work but will show a migration notice:

```bash
./scripts/deploy-frontend.sh
# ⚠️  This script has moved to scripts/deployment/frontend/deploy-frontend.sh
# 🔄 Redirecting to new location...
```

**Recommendation**: Update any direct script calls to use the new paths or npm scripts.

## ⚙️ Configuration

Before deploying, update the configuration in `scripts/deployment/shared/config.sh`:

```bash
# Required: Update these for your project
export BUCKET_NAME="your-unique-bucket-name"
export LAMBDA_FUNCTION_NAME="your-lambda-function-name"
export API_GATEWAY_NAME="your-api-gateway-name"

# Optional: Customize other settings
export AWS_REGION="us-east-1"
export CORS_ORIGINS="https://yourdomain.com,http://localhost:3000"
```

### Prerequisites

1. **AWS CLI** installed and configured
2. **Node.js 18+** and npm
3. **jq** for JSON processing
4. AWS credentials with appropriate permissions for:
   - S3 (create buckets, upload files)
   - CloudFront (create distributions)
   - Lambda (create/update functions)
   - API Gateway (create/update APIs)
   - IAM (create roles and policies)
