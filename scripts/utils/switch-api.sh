#!/bin/bash

# API Configuration Switcher
# This script helps you switch between local and deployed API endpoints

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOCAL_API="http://localhost:3001/api"
DEPLOYED_API="https://[API_GATEWAY_ID].execute-api.us-east-1.amazonaws.com/prod/api"
ENV_FILE=".env.local"

# Try to get the actual deployed API URL from AWS if available
get_deployed_api_url() {
    if command -v aws &> /dev/null && aws sts get-caller-identity &>/dev/null; then
        local api_id=$(aws apigateway get-rest-apis \
            --query "items[?name=='answer-bot-bloodandtreasure-api-gateway'].id" \
            --output text 2>/dev/null || echo "")

        if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
            echo "https://$api_id.execute-api.us-east-1.amazonaws.com/prod/api"
        else
            echo "$DEPLOYED_API"
        fi
    else
        echo "$DEPLOYED_API"
    fi
}

# Function to show usage
show_usage() {
    echo -e "${BLUE}🔗 API Configuration Switcher${NC}"
    echo
    echo "Usage: $0 [local|deployed|status|help]"
    echo
    echo "Commands:"
    echo "  local     - Configure frontend to use localhost API (default in dev mode)"
    echo "  deployed  - Configure frontend to use deployed AWS API"
    echo "  status    - Show current API configuration"
    echo "  help      - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 local     # Use localhost:3001 API"
    echo "  $0 deployed  # Use AWS API Gateway"
    echo "  $0 status    # Check current configuration"
}

# Function to set API configuration
set_api_config() {
    local api_type=$1
    local api_url=""

    case $api_type in
        "local")
            api_url="$LOCAL_API"
            ;;
        "deployed")
            api_url=$(get_deployed_api_url)
            ;;
        *)
            echo -e "${RED}❌ Invalid API type: $api_type${NC}"
            show_usage
            exit 1
            ;;
    esac
    
    # Create or update .env.local file
    if [ -f "$ENV_FILE" ]; then
        # Remove existing VITE_API_BASE_URL line
        grep -v "^VITE_API_BASE_URL=" "$ENV_FILE" > "${ENV_FILE}.tmp" || true
        mv "${ENV_FILE}.tmp" "$ENV_FILE"
    else
        # Create new file with header
        cat > "$ENV_FILE" << EOF
# Local Development Environment Variables
# This file is used when running the frontend in development mode

EOF
    fi
    
    # Add the new API configuration
    echo "VITE_API_BASE_URL=$api_url" >> "$ENV_FILE"
    
    echo -e "${GREEN}✅ API configured for $api_type mode${NC}"
    echo -e "${BLUE}📍 API URL: $api_url${NC}"
    echo
    echo -e "${YELLOW}💡 Restart your dev server for changes to take effect${NC}"
}

# Function to show current status
show_status() {
    echo -e "${BLUE}🔍 Current API Configuration${NC}"
    echo
    
    if [ -f "$ENV_FILE" ]; then
        local current_api=$(grep "^VITE_API_BASE_URL=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2)
        
        if [ -n "$current_api" ]; then
            echo -e "${GREEN}📍 Configured API: $current_api${NC}"
            
            if [ "$current_api" = "$LOCAL_API" ]; then
                echo -e "${GREEN}🏠 Mode: Local development${NC}"
                echo -e "${YELLOW}💡 Make sure your backend is running: npm run dev:backend${NC}"
            elif [ "$current_api" = "$DEPLOYED_API" ]; then
                echo -e "${GREEN}☁️  Mode: Deployed AWS API${NC}"
                echo -e "${YELLOW}💡 Using production API Gateway${NC}"
            else
                echo -e "${YELLOW}⚠️  Mode: Custom API endpoint${NC}"
            fi
        else
            echo -e "${YELLOW}📍 No explicit API configured${NC}"
            echo -e "${BLUE}🏠 Default: Will use localhost in dev mode, deployed in production${NC}"
        fi
    else
        echo -e "${YELLOW}📍 No .env.local file found${NC}"
        echo -e "${BLUE}🏠 Default: Will use localhost in dev mode, deployed in production${NC}"
    fi
    
    echo
    echo -e "${BLUE}Available APIs:${NC}"
    echo -e "  🏠 Local:    $LOCAL_API"
    echo -e "  ☁️  Deployed: $DEPLOYED_API"
}

# Function to check if backend is running locally
check_local_backend() {
    echo -e "${BLUE}🔍 Checking local backend status...${NC}"
    
    if curl -s "http://localhost:3001/api/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Local backend is running${NC}"
        return 0
    else
        echo -e "${RED}❌ Local backend is not running${NC}"
        echo -e "${YELLOW}💡 Start it with: npm run dev:backend${NC}"
        return 1
    fi
}

# Main function
main() {
    local command=${1:-status}
    
    case $command in
        "local")
            set_api_config "local"
            echo
            check_local_backend
            ;;
        "deployed")
            set_api_config "deployed"
            ;;
        "status")
            show_status
            if [ -f "$ENV_FILE" ]; then
                local current_api=$(grep "^VITE_API_BASE_URL=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2)
                if [ "$current_api" = "$LOCAL_API" ]; then
                    echo
                    check_local_backend
                fi
            fi
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $command${NC}"
            echo
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
