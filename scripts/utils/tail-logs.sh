#!/bin/bash

# Convenient wrapper for Lambda log tailing
# This script provides easy access to the Lambda log tailing functionality

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Path to the actual log tailing script
LOG_TAIL_SCRIPT="$SCRIPT_DIR/../connect/backend/tail-lambda-logs.sh"

# Check if the log tailing script exists
if [ ! -f "$LOG_TAIL_SCRIPT" ]; then
    echo "❌ Error: Log tailing script not found at $LOG_TAIL_SCRIPT"
    exit 1
fi

# Make sure it's executable
chmod +x "$LOG_TAIL_SCRIPT"

# Pass all arguments to the actual script
exec "$LOG_TAIL_SCRIPT" "$@"
