# Utility Scripts

This directory contains utility scripts for the AI Answer Bot Demo project.

## 🔧 Available Scripts

### `switch-api.sh`
Switch between local development API and deployed AWS API endpoints.

**Usage:**
```bash
npm run switch:api [local|deployed|status]

# Examples
npm run switch:api local     # Use localhost:3001 API
npm run switch:api deployed  # Use AWS API Gateway
npm run switch:api status    # Check current configuration
```

**What it does:**
- Updates `.env.local` with the appropriate `VITE_API_BASE_URL`
- Automatically detects deployed API Gateway URL if AWS CLI is configured
- Provides status information about current configuration

### `tail-logs.sh`
Wrapper script for tailing Lambda logs.

**Usage:**
```bash
npm run logs:tail [options]

# Examples
npm run logs:tail           # Tail logs from last 10 minutes
npm run logs:tail -- -f 1h  # Tail logs from last hour
npm run logs:tail -- --help # Show all options
```

**What it does:**
- Provides easy access to Lambda log tailing functionality
- Forwards all arguments to the actual log tailing script
- Automatically sets up AWS credentials

## 🔗 Related Scripts

These utility scripts work with other scripts in the project:

- **Deployment scripts**: `scripts/deployment/` - Deploy and manage AWS infrastructure
- **Connection scripts**: `scripts/connect/` - Connect to and manage deployed backend
- **Configuration**: `scripts/deployment/shared/config.sh` - Central configuration

## 📝 Notes

- All scripts that interact with AWS automatically run `npm run set:credentials` when called via npm scripts
- Use npm scripts instead of calling scripts directly for best results
- Scripts are designed to work from the project root directory
